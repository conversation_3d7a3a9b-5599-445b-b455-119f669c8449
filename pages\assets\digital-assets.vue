<template>
	<view>
		<!-- 顶部标签栏 -->
		<view class="tabbar-container">
			<template v-if="isIp">
				<view class="list2">
					<view v-for="(item, index) in list2" :key="item.id" class="list2-item" @click="getAdd(item)"
						:style="index === 0 ? 'grid-area: item1;' : index === 1 ? 'grid-area: item2;' : 'grid-area: item3;'">
						<image class="img" :src="imgUrl + item.img"></image>
						<view class="title">{{item.title}}</view>
						<view class="desc">{{item.desc}}</view>
					</view>
				</view>
			</template>
			<scroll-view scroll-x="true" class="tabbar-scroll" :scroll-with-animation="true" :show-scrollbar="false">
				<view class="tabbar-wrapper">
					<view v-for="(item,index) in tabs" :key="index" class="tabbar-item"
						:class="{'tabbar-item-active': tabsId == item.id}" @click="getId(item)">
						<text class="tabbar-text">{{item.name}}</text>
						<view class="tabbar-line" :class="{'tabbar-line-active': tabsId == item.id}"></view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 子标签栏 -->
		<view class="sub-tabbar-container" v-if="nextList && nextList.length > 0">
			<scroll-view scroll-x="true" class="sub-tabbar-scroll" :scroll-with-animation="true"
				:show-scrollbar="false">
				<view class="sub-tabbar-wrapper">
					<view v-for="(items,indexs) in nextList" :key="indexs" class="sub-tabbar-item"
						:class="{'sub-tabbar-item-active': tabsNextId == items.id}" @click="getNextId(items.id)">
						<text>{{items.name}}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 视频过期提示 -->
		<block v-if="tabsId == 2 && type == 1">
			<view v-if="list.length > 0" class="color_FFFFFF font-size_26rpx" style="padding: 0 20rpx 20rpx;">注意:
				视频生成成功后只保留{{cloneSet.video_expire}}天,过期后自动删除</view>
		</block>

		<!-- 主内容区域 -->
		<block v-if="userUid">
			<mescroll-body ref="mescrollRef" :isShowEmptys="true" :height="windowHeight+'rpx'" @init="mescrollInit"
				@down="downCallback" @up="upCallback" :up="upOption" :down="downOption">

				<!-- 有数据时的内容展示 -->
				<block v-if="list.length > 0">
					<!-- 我的形象 tabsId=1 -->
					<block v-if="tabsId == 1">
						<view class="display-fw-a" style="padding: 0 20rpx;justify-content: space-between;">
							<block v-for="(item,index) in list" :key="index">
								<view style="position: relative;">
									<view class="service-life" v-if="item.ascription_type == 2">
										<view class="font-size_24rpx" v-if="item.buy_expire == 1">使用期限: 已过期</view>
										<view class="font-size_24rpx" v-else>使用期限:
											{{item.effective_time == 0 ? '永久' : item.effective_time}}
										</view>
									</view>

									<view class="frame" @click="getVideo(item)">
										<view class="failed-tips display-a-jc" style="color: #fff;"
											v-if="item.is_status == 1">管理员审核中</view>
										<view class="failed-tips display-a-jc" v-if="item.is_status == 3">
											{{item.refuse}}
										</view>
										<view class="failed-tips display-ac-jc" @click="getStartTraining(item.id)"
											v-if="item.is_status == 2 && tabsNextId == 1 && item.current_status == 'nothing'">
											<image class="img-347" :src="imgUrl+'347.png'"></image>
											<view class="color_FFFFFF font-size_26rpx">立即克隆</view>
										</view>
										<view class="failed-tips display-ac-jc" @click="getStartTraining(item.id)"
											v-if="item.is_status == 2 && tabsNextId == 2 && item.new_current_status == 'nothing'">
											<image class="img-347" :src="imgUrl+'347.png'"></image>
											<view class="color_FFFFFF font-size_26rpx">立即克隆</view>
										</view>
										<view class="failed-tips display-ac-jc" @click="getStartTraining(item.id)"
											v-if="item.is_status == 2 && tabsNextId == 3 && item.composite_current_status == 'nothing'">
											<image class="img-347" :src="imgUrl+'347.png'"></image>
											<view class="color_FFFFFF font-size_26rpx">立即克隆</view>
										</view>
										<view class="failed-tips display-ac-jc" @click="getStartTraining(item.id)"
											v-if="item.is_status == 2 && tabsNextId == 4 && item.four_current_status == 'nothing'">
											<image class="img-347" :src="imgUrl+'347.png'"></image>
											<view class="color_FFFFFF font-size_26rpx">立即克隆</view>
										</view>
										<block v-if="item.is_status == 2 && tabsNextId == 1">
											<view class="r-status r-status-4" v-if="item.current_status == 'nothing'" ﻿>
												未训练</view>
											<view class="r-status r-status-2"
												v-else-if="item.current_status == 'completed'">已完成</view>
											<view class="r-status r-status-3"
												v-else-if="item.current_status == 'failed'">已失败</view>
											<view class="r-status r-status-1" v-else>克隆中</view>
										</block>
										<block v-if="item.is_status == 2 && tabsNextId == 2">
											<view class="r-status r-status-4"
												v-if="item.new_current_status == 'nothing'">未训练</view>
											<view class="r-status r-status-2"
												v-else-if="item.new_current_status == 'completed'">已完成</view>
											<view class="r-status r-status-3"
												v-else-if="item.new_current_status == 'failed'">已失败</view>
											<view class="r-status r-status-1" v-else>克隆中</view>
										</block>
										<block v-if="item.is_status == 2 && tabsNextId == 3">
											<view class="r-status r-status-4"
												v-if="item.composite_current_status == 'nothing'">未训练</view>
											<view class="r-status r-status-2"
												v-else-if="item.composite_current_status == 'completed'">已完成</view>
											<view class="r-status r-status-3"
												v-else-if="item.composite_current_status == 'failed'">已失败</view>
											<view class="r-status r-status-1" v-else>克隆中</view>
										</block>
										<block v-if="item.is_status == 2 && tabsNextId == 4">
											<view class="r-status r-status-4"
												v-if="item.four_current_status == 'nothing'">未训练</view>
											<view class="r-status r-status-2"
												v-else-if="item.four_current_status == 'completed'">已完成</view>
											<view class="r-status r-status-3"
												v-else-if="item.four_current_status == 'failed'">已失败</view>
											<view class="r-status r-status-1" v-else>克隆中</view>
										</block>
										<view class="r-status r-status-1" v-if="item.is_status == 1">审核中</view>
										<view class="r-status r-status-3" v-if="item.is_status == 3">已驳回</view>

										<view class="failed-tips display-a-jc"
											style="font-size: 20rpx;padding: 0 10rpx;"
											v-if="item.current_status == 'failed'">{{item.task_message}}</view>
										<image class="img-129" :src="imgUrl + '138.png'"></image>
										<image class="r-video" mode="widthFix" :src="item.video_cover"></image>
									</view>
									<view class="frame-title">
										<view class="display-a">
											<view style="width: 160rpx;"
												class="font-size_24rpx font-overflow color_FFFFFF">{{item.name}}
											</view>
											<image
												v-if="(tabsNextId == 1 && item.current_status == 'completed') || (tabsNextId == 2 && item.new_current_status == 'completed') || (tabsNextId == 3 && item.composite_current_status == 'completed') || (tabsNextId == 4 && item.four_current_status == 'completed')"
												@click="getBatch(item)" class="img-59" :src="imgUrl + '59.png'"></image>
											<image v-else @click="delVideo(item)" class="img-59"
												:src="imgUrl + '73.png'"></image>
										</view>
									</view>
								</view>
							</block>
						</view>
					</block>

					<!-- 我的作品 tabsId=2 type=1 -->
					<block v-if="tabsId == 2 && type == 1">
						<view class="display-fw-a" style="padding: 0 20rpx;justify-content: space-between;">
							<block v-for="(item,index) in list" :key="index">
								<view v-if="item.id">
									<view class="r-frame-w">
										<view class="r-status r-status-2" v-if="item.current_status == 'success'">已完成
										</view>
										<view class="r-status r-status-3" v-else-if="item.current_status == 'fail'">已失败
										</view>
										<view class="r-status r-status-1"
											v-else-if="item.current_status == 'wait_sent'">等待中</view>
										<view class="r-status r-status-4" v-else>生成中</view>
										<block v-if="item.current_status == 'success'">
											<block v-if="item.result_cover">
												<image class="img-129" :src="imgUrl + '134.png'"></image>
												<image @click="getVideoOpen(item)" class="img-158"
													:src="imgUrl+'158.png'">
												</image>
												<image @click="getVideoOpen(item)" class="r-video-w" mode="widthFix"
													:src="item.result_cover"></image>
											</block>
											<block v-else>
												<image class="img-129" :src="imgUrl + '134.png'"></image>
												<image @click="getVideoOpen(item)" class="img-158"
													:src="imgUrl+'158.png'">
												</image>
												<image @click="getVideoOpen(item)" class="r-video-w" mode="widthFix"
													:src="item.local_cover"></image>
											</block>
										</block>
										<block v-else-if="item.current_status == 'fail'">
											<view class="r-img-bg"
												:style="{'background-image': 'url('+imgUrl+'132.png'+')'}">
												<view class="r-text color_FF0000">视频合成失败</view>
											</view>
										</block>
										<block v-else>
											<view class="r-img-bg"
												:style="{'background-image': 'url('+imgUrl+'131.png'+')'}">
												<view class="r-text color_999999">生成中,请等待...</view>
											</view>
										</block>
									</view>
									<view class="frame-title margin-bottom_20rpx">
										<view class="display-a-js padding-bottom_10rpx margin-bottom_10rpx p-bo2">
											<view style="width: 210rpx;"
												class="font-size_26rpx font-overflow color_FFFFFF">
												{{item.name}}
											</view>
										</view>
										<view class="display-a">
											<view class="text-align_center" style="width: 70rpx;"
												@click="longpress(item.local_url,item.result,item.current_status)">
												<image class="img-362" :src="imgUrl+'362.png'"></image>
											</view>
											<view class="text-align_center" style="width: 70rpx;"
												@click="getCopy(item.result,item.current_status)">
												<image class="img-362" :src="imgUrl+'363.png'"></image>
											</view>
											<view class="text-align_center" style="width: 70rpx;"
												@click="delVideo(item)">
												<image class="img-362" :src="imgUrl+'364.png'"></image>
											</view>
										</view>
									</view>
								</view>
							</block>
						</view>
					</block>

					<!-- 成品库 tabsId=4 -->
					<block v-if="tabsId == 4">
						<block v-for="(item,index) in list" :key="index">
							<view v-if="item.id" class="display-a task-list">
								<image @click="getVideoList(item)" class="img-306" :src="imgUrl+'306.png'"></image>
								<view style="width: 450rpx;">
									<view class="display-a margin-bottom_20rpx" @click="getVideoList(item)">
										<view style="max-width: 324rpx;"
											class="color_FFFFFF font-size_32rpx font-overflow">{{item.title}}</view>
										<view class="w-status w-status-1" v-if="item.status < 4">合成中</view>
										<view class="w-status w-status-2" v-if="item.status == 4">已完成</view>
										<view class="w-status w-status-3" v-if="item.status == 5">已失败</view>
										<view class="w-status w-status-2" v-if="item.status == 6">部分成功</view>
									</view>
									<view class="display-a">
										<view class="color_A3A3A3 font-size_26rpx">{{item.create_time}}</view>
									</view>
								</view>
								<view @click="getVideoList(item)" class="margin-left-auto color_A3A3A3 font-size_26rpx">
									{{item.video_count}}个
								</view>
								<image @click="getVideoList(item)" class="img-21" :src="imgUrl+'21.png'"></image>
							</view>
						</block>
					</block>

					<!-- 我的音色 tabsId=3 -->
					<block v-if="tabsId == 3">
						<block v-for="(item,index) in list" :key="index">
							<view class="list-public">
								<view class="display-a margin-bottom_20rpx">
									<view class="service-life-2" v-if="item.ascription_type == 2">
										<view class="font-size_24rpx" v-if="item.buy_expire == 1">使用期限: 已过期</view>
										<view class="font-size_24rpx" v-else>使用期限:
											{{item.effective_time == 0 ? '永久' : item.effective_time}}
										</view>
									</view>
									<block v-if="item.is_status == 2">
										<view class="ra-status"
											:class="item.current_status == 'completed' ? 'color_FFFFFF' : item.current_status == 'failed' ? 'color_FF0000' : 'ra-status-1'">
											{{item.current_status == 'completed' ? '已完成' : item.current_status == 'failed' ? '已失败' : '未完成'}}
										</view>
									</block>
									<block v-else>
										<view class="ra-status"
											:class="item.is_status == 3 ? 'color_FF0000' : 'ra-status-1'">
											{{item.is_status == 3 ? '已驳回' : '审核中'}}
										</view>
									</block>
									<image class="img-246" :src="imgUrl+'246.png'"></image>
									<view>
										<view class="display-a">
											<view class="r-fast"
												:class="item.train_mode == 1 ? 'r-fast-1' : 'r-fast-2'">
												{{item.train_mode == 1 ? '入门版' : item.train_mode == 2 ? '高保真' : '专业版'}}
											</view>
											<view class="r-name font-overflow">{{item.name}}</view>
										</view>
										<view class="display-a" style="margin-top: 16rpx;">
											<view class="r-date">{{item.create_time}}</view>
											<image @click="delAudio(item.id,item.name)" class="img-96"
												:src="imgUrl+'73.png'"></image>
											<view @click="delAudio(item.id,item.name)"
												class="color_B7B7B7 font-size_26rpx">删除</view>
										</view>
									</view>
									<block
										v-if="configSet.resource_open == 1 && item.train_mode == 2 && item.current_status == 'completed' && item.ascription_type == 1 && item.is_share == 1">
										<image @click="getShare(2,item)" class="img-251" :src="imgUrl+'251.png'">
										</image>
										<view @click="getShare(2,item)" class="color_FFFFFF font-size_32rpx">共享</view>
									</block>
									<block v-if="item.train_mode == 2">
										<view @click="getAgain(item)" class="again"
											v-if="item.is_status == 3 || item.current_status == 'failed'">重新克隆</view>
									</block>
								</view>
								<view class="reject" v-if="item.is_status == 3">
									驳回理由: {{item.refuse?item.refuse:'声音质量较差'}}
								</view>
							</view>
						</block>
					</block>

					<!-- 资产市场 tabsId=5 -->
					<block v-if="tabsId == 5">
						<block v-if="tabsNextId == 1">
							<!-- 形象资产 -->
							<view class="display-fw-a" style="padding: 0 20rpx;justify-content: space-between;">
								<block v-for="(item,index) in list" :key="index">
									<view>
										<view class="frame" style="width: 344rpx;height: 612rpx;">
											<!-- 标准 -->
											<image v-if="item.fca_url" class="r-video" style="width: 344rpx;"
												mode="widthFix"
												:src="item.fca_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'">
											</image>
											<!-- 高级  -->
											<image v-else class="r-video" style="width: 344rpx;" mode="widthFix"
												:src="item.fa_video_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'">
											</image>
										</view>
										<view class="frame-title" style="width: 344rpx;">
											<view style="width: 320rpx;"
												class="font-overflow font-size_30rpx color_FFFFFF margin-bottom_10rpx">
												{{item.title}}
											</view>
											<view class="display-a-js margin-bottom_20rpx color_FF0000">
												<view>收益: <span class="font-weight_bold">￥{{item.sell_profit}}</span>
												</view>
											</view>
											<view class="display-a">
												<block v-if="item.status == 2">
													<image @click="getShare(4,item)" class="img-252"
														:src="imgUrl+'252.png'"></image>
													<image @click="delVideo(item)" class="img-252"
														:src="imgUrl+'253.png'"></image>
												</block>
												<view class="shelves margin-left-auto" @click="getShelves(item)"
													:class="item.status == 1 ? 'shelves-2' : 'shelves-1'">
													{{item.status == 1 ? '下架' : '上架'}}
												</view>
											</view>
										</view>
									</view>
								</block>
							</view>
						</block>
						<block v-else-if="tabsNextId == 2">
							<!-- 声音资产 -->
							<block v-for="(item,index) in list" :key="index">
								<view class="display-a padding_20rpx color_FFFFFF margin-bottom_20rpx"
									style="position: relative;">
									<image class="img-266" :src="imgUrl+'266.png'"></image>
									<image class="img-254" :key="updateKey"
										@click="playAudio(item.isPlay,index,item.fat_id,item.fat_media_url)"
										:src="item.isPlay == 1 ? imgUrl+'255.png' : imgUrl+'254.png'"></image>
									<view style="width: 550rpx;">
										<view class="display-a margin-bottom_20rpx">
											<view style="width: 310rpx;"
												class="font-size_32rpx margin-bottom_10rpx font-overflow">{{item.title}}
											</view>
											<view class="shelves margin-left-auto" @click="getShelves(item)"
												:class="item.status == 1 ? 'shelves-2' : 'shelves-1'">
												{{item.status == 1 ? '下架' : '上架'}}
											</view>
										</view>
										<view class="display-a color_FF0000">
											<view class="">收益: ￥{{item.sell_profit}}</view>
											<view class="margin-left-auto" v-if="item.status == 2">
												<image @click="getShare(4,item)" class="img-253"
													:src="imgUrl+'252.png'"></image>
												<image @click="delVideo(item)" class="img-253" :src="imgUrl+'253.png'">
												</image>
											</view>
										</view>
									</view>
								</view>
							</block>
						</block>
					</block>
				</block>

				<!-- 无数据时的提示 -->
				<block v-else>
					<view class="display-ac-jc">
						<!-- 我的形象 -->
						<block v-if="tabsId == 1">
							<image class="nodata" src="../../static/nodata.png"></image>
							<view class="nodata-tips">您还没有数字人，快去定制一个吧~</view>
							<view class="nodata-but" @click="getClone()">创建数字人</view>
						</block>

						<!-- 我的作品 -->
						<block v-if="tabsId == 2 && type == 1">
							<image class="nodata" src="../../static/nodata.png"></image>
							<view class="nodata-tips">您还没有作品，快去创作一个吧~</view>
							<view class="nodata-but" @click="getClip()">去创建作品</view>
						</block>

						<!-- 成品库 -->
						<block v-if="tabsId == 4">
							<image class="nodata" src="../../static/nodata.png"></image>
							<view class="nodata-tips">您还没有剪辑成品，快去剪辑一个吧~</view>
							<view class="nodata-but" @click="getEdit()">去剪辑</view>
						</block>

						<!-- 我的音色 -->
						<block v-if="tabsId == 3">
							<image class="nodata" :src="imgUrl+'tabbar/nodata4.png'"></image>
							<view class="nodata-tips">您还没有声音资产，快去复刻一个吧~</view>
							<view class="nodata-but" @click="getClone()">立即复刻声音</view>
						</block>

						<!-- 资产市场 -->
						<block v-if="tabsId == 5">
							<image class="nodata" src="../../static/nodata.png"></image>
							<view class="nodata-tips">您还没有共享资产~</view>
						</block>
					</view>
				</block>
			</mescroll-body>
		</block>

		<!-- 未登录提示 -->
		<block v-else>
			<view class="display-ac-jc">
				<image class="nodata" src="../../static/nodata.png"></image>
				<view class="nodata-tips">请先授权查询数据~</view>
				<view class="nodata-but" @click="getAuth()">去授权</view>
			</view>
		</block>

		<!-- 各种弹窗组件 -->

		<!-- 加载弹窗 -->
		<xuan-loading ref="loading" :custom="false" :shadeClick="true" :tips="loadingTips" :type="1"
			@callback="loadingCallback()">
		</xuan-loading>

		<!-- 批量操作弹窗 -->
		<sunui-popup ref="pop2">
			<template v-slot:content>
				<view @click="delVideo(obj)" class="p-data p-bo color_FF0000" style="padding: 40rpx 0 40rpx;">删除</view>

				<!-- 形象共享 -->
				<block v-if="tabsId == 1">
					<block v-if="listIdOne == 1 && listIdTwo == 1 && listIdThree == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.current_status == 'completed' && obj.new_current_status == 'completed' && obj.composite_current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
					<block v-else-if="listIdOne == 1 && listIdTwo == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.current_status == 'completed' && obj.new_current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
					<block v-else-if="listIdOne == 1 && listIdThree == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.current_status == 'completed' && obj.composite_current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
					<block v-else-if="listIdTwo == 1 && listIdThree == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.new_current_status == 'completed' && obj.composite_current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
					<block v-else-if="listIdOne == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
					<block v-else-if="listIdTwo == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.new_current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
					<block v-else-if="listIdThree == 1">
						<view
							v-if="configSet.resource_open == 1 && obj.composite_current_status == 'completed' && obj.ascription_type == 1 && obj.is_share == 1"
							@click="getShare(1,'')" class="p-data p-bo">共享形象</view>
					</block>
				</block>

				<!-- 我的作品弹窗选项 -->
				<block v-if="tabsId == 2">
					<view v-if="obj.current_status == 'success'" @click="longpress()" class="p-data p-bo">保存到相册</view>
					<view v-if="obj.current_status == 'success'" @click="getCopy()" class="p-data p-bo">复制视频链接</view>
				</block>

				<view class="p-data" @click="closeBatch()">取消</view>
			</template>
		</sunui-popup>

		<!-- 共享协议弹窗 -->
		<sunui-popup ref="pop">
			<template v-slot:content>
				<view style="overflow:auto;padding:10rpx 30rpx 20rpx;">
					<scroll-view :scroll-y="true" style="height: 700rpx;">
						<rich-parser :html="configSet.figure_release_agreement"
							domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article" selectable
							show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>

					<view class="display-a-js margin-top_20rpx ">
						<view @click="close()" class="c-agree">我已阅读并同意</view>
					</view>
				</view>
			</template>
		</sunui-popup>

		<!-- 共享资产弹窗 -->
		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="pop-bg">
					<image @click="closeShare()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">共享资产</view>

					<view class="font-size_30rpx margin-bottom_20rpx">共享标题<span
							class="color_FF0000 margin-left_10rpx">*</span></view>
					<input type="text" v-model="title" class="p-input margin-bottom_40rpx" placeholder="请输入共享标题"
						placeholder-class="placeholder" />
					<view class="font-size_30rpx margin-bottom_20rpx">共享时长<span
							class="color_FF0000 margin-left_10rpx">*</span></view>
					<scroll-view :scroll-x="true" class="margin-bottom_10rpx"
						style="width: 700rpx; white-space: nowrap;" :scroll-with-animation="true">
						<view class="display-a">
							<block v-for="(item,index) in timeList" :key="index">
								<view @click="getTabs(item,index)" class="tabs-a"
									:class="priceIndex == index ? 'tabs-a-2' : 'tabs-a-1'">{{item.name}}</view>
							</block>
						</view>
					</scroll-view>
					<view class="font-size_24rpx color_FFFFFF margin-bottom_40rpx">注: 时长/时长价格都设置完才能上架</view>
					<view class="font-size_30rpx margin-bottom_20rpx">共享时长价格<span
							class="color_FF0000 margin-left_10rpx">*</span></view>
					<input type="digit" v-model="price[priceIndex].price" class="p-input margin-bottom_20rpx"
						placeholder="请输入共享价格" placeholder-class="placeholder" />
					<view class="font-size_26rpx">提示: 平台抽佣<span
							class="color_FF0000 margin_0_4rpx">{{configSet.release_platform_points}}%</span>,佣金可在我的收益里申请提现
					</view>
					<view style="height: 140rpx;"></view>
					<block v-if="!assetsId">
						<view class="display-a-jc margin-bottom_20rpx" v-if="configSet.figure_release_agreement">
							<image @click="getIsProtocol()" class="img-219"
								:src="isProtocol ? imgUrl+'220.png' : imgUrl+'219.png'">
							</image>
							<view @click="openProtocol()" class="color_CBCACA">同意并且确认<span
									class="color_4BA2FF">《{{tabsId == 3 ? '共享声音资产协议' : '共享形象资产协议'}}》</span></view>
						</view>
					</block>
					<view class="c-agree" @click="getReleaseGood()">{{assetsId ? '确认修改' : '确认共享资产'}}</view>
				</view>
			</template>
		</sunui-popup>

		<!-- 客服二维码弹窗 -->
		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<!-- 客服联系提示弹窗 -->
		<sunui-popup ref="kefu">
			<template v-slot:content>
				<view class="h_30rpx"></view>
				<view class="color_FFFFFF text-align_center">请联系客服进行审核</view>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<!-- 视频播放弹窗 -->
		<sunui-popup ref="rescuePop">
			<template v-slot:content>
				<view style="width: 540rpx;height: 960rpx;border-radius: 10rpx;">
					<video style="width: 540rpx;height: 960rpx;border-radius: 10rpx;" :poster="obj.local_cover"
						:src="obj.result"></video>
				</view>
			</template>
		</sunui-popup>

		<!-- 客服悬浮按钮 -->
		<block v-if="customerConfig.customer_type == 'on_line'">
			<button open-type="contact">
				<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl+'kefu.gif'">
				</image>
			</button>
		</block>
		<block v-else>
			<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl+'kefu.gif'">
			</image>
		</block>

		<!-- 底部导航栏 -->
		<sunui-tabbar v-if="!isIp" :fixed="true" :current="tabIndex" :types="1"></sunui-tabbar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list2: [],
				isIp: false,
				// 合并后的数据
				tabs: [{
						id: 1,
						name: '形象',
						list: uni.getStorageSync('indexWay') || []
					},
					{
						id: 2,
						name: '作品',
						list: uni.getStorageSync('indexWay') || []
					},
					{
						id: 3,
						name: '音色',
						list: [{
								id: 1,
								name: '入门版'
							},
							{
								id: 3,
								name: '专业版'
							}, {
								id: 2,
								name: '高保真'
							}
						]
					},
					{
						id: 4,
						name: '成品库',
						list: []
					},
					{
						id: 5,
						name: '资产市场',
						list: [{
							id: 1,
							name: '形象资产'
						}, {
							id: 2,
							name: '声音资产'
						}]
					}
				],

				tabsId: 1, // 当前选中的主标签
				tabsNextId: '', // 当前选中的子标签
				nextList: [], // 当前子标签列表

				tabIndex: 2, // tabbar当前选中项

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: true,
					empty: {
						show(auto) {}
					}
				},

				list: [], // 列表数据
				windowHeight: '', // 窗口高度
				imgUrl: this.$imgUrl, // 图片URL前缀

				updateKey: false, // 用于音频播放状态更新
				isWhether: true, // 防止重复点击
				voiceAudioContext: null, // 音频播放上下文

				cloneSet: {}, // 克隆设置
				userUid: uni.getStorageSync('uid'), // 用户ID
				sign: '', // 标记

				obj: {}, // 当前操作对象
				configSet: {}, // 配置设置

				timeList: [], // 时间段列表
				title: '', // 共享标题
				price: [], // 价格列表
				priceId: [], // 价格ID
				priceIndex: 0, // 当前价格索引
				assetsId: '', // 资产ID

				listIdOne: '', // 线路一状态(1开启)
				listIdTwo: '', // 线路二状态(1开启)
				listIdThree: '', // 线路三状态(1开启)

				isProtocol: false, // 是否同意协议
				isKefu: true, // 客服按钮状态(true隐藏 false展开)
				customerConfig: uni.getStorageSync('customerConfig'), // 客服配置

				loadingTips: 'Loading', // 加载提示文本
				type: '1', // 作品类型 1我的作品 4成片库
				name: '', // 搜索名称

				system: uni.getStorageSync('system'), // 系统信息
			}
		},

		onLoad(options) {
			// 获取窗口高度，设置滚动区域高度
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					// 根据不同标签页设置不同的高度
					if (this.tabsId == 2 && this.type == 1) {
						this.windowHeight = res.windowHeight * 2 - 460;
					} else {
						this.windowHeight = res.windowHeight * 2 - 420;
					}
				},
			});

			if (options.isIp) {
				this.isIp = true
				uni.setNavigationBarTitle({
					title: '数字人IP'
				})
				this.list2 = [{
					id: 12,
					title: '创建数字人视频',
					img: 'n2-12.png',
					desc: '上传视频极速生成'
				}, {
					id: 10,
					title: '声音克隆',
					img: 'n2-10.png',
					desc: '仅需30秒声音复刻'
				}, {
					id: 11,
					title: '形象克隆',
					img: 'n2-11.png',
					desc: '创作你的数字人分身'
				}]
			}

			// 获取初始化数据
			this.getIndexWay();
			this.getReleaseConfig();
			this.getTimeList();
			this.indexkfSet();
		},

		onShow() {
			this.getIndexWay();

			let pagearr = getCurrentPages(); //获取应用页面栈
			let currentPage = pagearr[pagearr.length - 1]; //获取当前页面信息

			// 处理页面参数
			if (currentPage.options.sign) {
				this.sign = currentPage.options.sign;
				this.getCloneSet(1);
			}
			if (currentPage.options.tabsNextId) {
				this.tabsNextId = currentPage.options.tabsNextId;
			}
			if (currentPage.options.tabsId) {
				this.tabsId = currentPage.options.tabsId;
				this.getCloneSet(2);
			}
			if (currentPage.options.type) {
				this.type = currentPage.options.type;
				if (this.type == 1) {
					this.tabsNextId = currentPage.options.tabsNextId || '';
				}
				this.getCloneSet();
			}
		},

		onUnload() {
			// 清理音频播放器
			if (this.voiceAudioContext && !this.voiceAudioContext.paused) {
				this.voiceAudioContext.stop();
			}
		},

		methods: {
			getAdd(item) {
				switch (item.id) {
					case 10:
						uni.navigateTo({
							url: "/pages/index/clone/middle?type=2",
						});
						break;
					case 11:
						uni.navigateTo({
							url: "/pages/index/clone/clone",
						});
						break;
					case 12:
						uni.navigateTo({
							url: "/pages/index/clip/clip",
						});
						break;
					default:
						break;
				}
			},
			// 公共方法

			// 线路自定义名称
			async getIndexWay() {
				const result = await this.$http.post({
					url: this.$api.indexWay
				});
				if (result.errno == 0) {
					uni.setStorageSync('indexWay', result.data);
					// 设置线路状态
					for (let i = 0; i < result.data.length; i++) {
						let listId = result.data[i].id;
						if (listId == 1) {
							this.listIdOne = 1;
						}
						if (listId == 2) {
							this.listIdTwo = 1;
						}
						if (listId == 3) {
							this.listIdThree = 1;
						}
					}
					// 更新tabs中的列表数据
					this.tabs[0].list = result.data;
					this.tabs[1].list = result.data;
				}
			},

			//去授权
			getAuth() {
				uni.redirectTo({
					url: '/pages/auth/auth?type=1'
				})
			},

			// 客服设置接口
			async indexkfSet() {
				const result = await this.$http.get({
					url: this.$api.kfSet
				})
				if (result.errno == 0) {
					if (result.data.clip_swtich != 1) {
						// 如果剪辑功能关闭，移除成品库标签
						for (let i = 0; i < this.tabs.length; i++) {
							if (this.tabs[i].id == 4) {
								this.tabs.splice(i, 1);
								break;
							}
						}
					}
				}
			},

			// 获取客服联系
			getKefu() {
				if (this.isKefu) {
					this.isKefu = false;
				} else {
					if (this.customerConfig.customer_type == 'on_line') {
						return;
					} else if (this.customerConfig.customer_type == 'phone') {
						if (this.customerConfig.customer_phone) {
							this.$sun.phone(this.customerConfig.customer_phone);
						} else {
							this.$sun.toast("暂无联系方式", 'error');
						}
					} else if (this.customerConfig.customer_type == 'qr_code') {
						this.$refs.pop5.show({
							style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
							bottomClose: true,
							shadeClose: false,
						});
					}
				}
			},

			//资产配置
			async getReleaseConfig() {
				const result = await this.$http.post({
					url: this.$api.releaseConfig
				});
				if (result.errno == 0) {
					this.configSet = result.data;
					if (this.configSet.resource_open != 1) {
						// 如果资产市场功能关闭，移除资产市场标签
						for (let i = 0; i < this.tabs.length; i++) {
							if (this.tabs[i].id == 5) {
								this.tabs.splice(i, 1);
								break;
							}
						}
					}
				}
			},

			//时间段
			async getTimeList() {
				const result = await this.$http.post({
					url: this.$api.getTime
				});
				if (result.errno == 0) {
					this.price = [];
					this.timeList = result.data;
					for (let i = 0; i < this.timeList.length; i++) {
						let priceObj = {
							name: this.timeList[i].name,
							index: this.timeList[i].index,
							day: this.timeList[i].day,
							price: '',
						}
						this.price.push(priceObj);
					}
				}
			},

			//克隆设置
			async getCloneSet(type) {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;

					if (type == 2) {
						if (this.tabsId == 1 && this.cloneSet.avatar_check == 1) {
							this.getOpenKf();
						}

						if (this.tabsId == 3) {
							if (this.cloneSet.voice_check == 1 && this.tabsNextId == 1) {
								this.getOpenKf();
							}
							if (this.cloneSet.voice_high_check == 1 && this.tabsNextId == 2) {
								this.getOpenKf();
							}
							if (this.cloneSet.xunfei_sound_clone_check == 1 && this.tabsNextId == 3) {
								this.getOpenKf();
							}
						}
					}

					// 处理音色线路
					if (this.cloneSet.voice_high_open != 1) { // 高保真
						for (let i = 0; i < this.tabs[2].list.length; i++) {
							if (this.tabs[2].list[i].id == 2) {
								this.tabs[2].list.splice(i, 1);
								break;
							}
						}
					}

					if (this.cloneSet.xunfei_sound_clone_swich != 1) { // 专业版
						for (let i = 0; i < this.tabs[2].list.length; i++) {
							if (this.tabs[2].list[i].id == 3) {
								this.tabs[2].list.splice(i, 1);
								break;
							}
						}
					}

					if (this.sign == 1) {
						this.tabsId = this.tabs[0].id;
						this.tabsNextId = this.tabs[0].list[0].id;
					}

					// 初始化子标签列表
					if (this.tabsId == 1) {
						this.nextList = this.tabs[0].list;
						// 如果 tabsNextId 为空，设置默认值
						if (!this.tabsNextId && this.nextList.length > 0) {
							this.tabsNextId = this.nextList[0].id;
						}
					} else if (this.tabsId == 2) {
						this.nextList = this.tabs[1].list;
						// 如果 tabsNextId 为空，设置默认值
						if (!this.tabsNextId && this.nextList.length > 0) {
							this.tabsNextId = this.nextList[0].id;
						}
					} else if (this.tabsId == 3) {
						this.nextList = this.tabs[2].list;
						// 如果 tabsNextId 为空，设置默认值
						if (!this.tabsNextId && this.nextList.length > 0) {
							this.tabsNextId = this.nextList[0].id;
						}
					} else if (this.tabsId == 5) {
						this.nextList = this.tabs[4].list;
						// 如果 tabsNextId 为空，设置默认值
						if (!this.tabsNextId && this.nextList.length > 0) {
							this.tabsNextId = this.nextList[0].id;
						}
					}

					// 更新数据列表
					if (uni.getStorageSync('uid')) {
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}
				}
			},

			// 打开客服二维码
			getOpenKf() {
				this.$refs.kefu.show({
					style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
					bottomClose: true,
					shadeClose: false,
				});
			},

			// 获取标签切换方法
			getId(obj) {
				if (uni.getStorageSync('uid')) {
					this.tabsId = obj.id;
					this.nextList = obj.list;
					if (this.nextList && this.nextList.length > 0) {
						this.tabsNextId = obj.list[0].id;
					} else {
						this.tabsNextId = '';
					}

					// 处理我的作品标签的特殊逻辑
					if (this.tabsId == 2) {
						this.type = 1; // 设置为我的作品类型
					}

					// 处理成品库标签的特殊逻辑
					if (this.tabsId == 4) {
						this.type = 4; // 设置为成品库类型
					}

					// 处理资产市场标签的特殊逻辑
					if (this.tabsId == 5) {
						this.type = 5; // 设置为资产市场类型
					}

					this.list = [];
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.redirectTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// 取消操作
							}
						}
					});
				}
			},

			// 获取子标签切换方法
			getNextId(id) {
				if (uni.getStorageSync('uid')) {
					this.tabsNextId = id;
					this.list = [];
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.redirectTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// 取消操作
							}
						}
					});
				}
			},

			// 加载操作相关
			loadingClose() {
				this.$refs.loading.close();
			},
			loadingOpen() {
				this.$refs.loading.open();
			},
			loadingCallback() {
				console.log("关闭后回掉");
			},

			// 导航回退
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			// 关闭批量操作弹窗
			closeBatch() {
				this.$refs.pop2.close();
			},

			// 关闭分享弹窗
			closeShare() {
				this.$refs.pop3.close();
			},

			// 创作作品
			getClip() {
				uni.navigateTo({
					url: '/pages/index/clip/clip?type=' + this.tabsNextId
				})
			},

			// 去剪辑
			getEdit() {
				uni.navigateTo({
					url: '/pages/edit/edit'
				})
			},

			// 创建数字人/声音资产
			getClone() {
				if (this.tabsId == 1) {
					let compressed = 1;
					if (this.tabsNextId == 4) {
						compressed = 2;
					}

					uni.navigateTo({
						url: '/pages/index/clone/clone?compressed=' + compressed
					})
				}
				if (this.tabsId == 3) {
					if (this.tabsNextId == 1) {
						uni.navigateTo({
							url: '/pages/index/voice/voice'
						})
					}
					if (this.tabsNextId == 2) {
						uni.navigateTo({
							url: '/pages/index/voice/senior'
						})
					}
					if (this.tabsNextId == 3) {
						uni.navigateTo({
							url: '/pages/index/voice/highFidelity'
						})
					}
				}
			},

			// 上架/下架
			getShelves(obj) {
				let contentT = obj.status == 1 ? '下架' : '上架';

				uni.showModal({
					content: "是否" + contentT + '该资产',
					cancelText: "取消",
					confirmText: "确认",
					success: async (res) => {
						if (res.confirm) {

							if (!this.isWhether) {
								return;
							}
							this.isWhether = false;

							const result = await this.$http.post({
								url: this.$api.updateStatus,
								data: {
									uid: uni.getStorageSync('uid'),
									id: obj.id,
									status: obj.status == 1 ? 2 : 1, //1上架 2 下架
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
									this.isWhether = true;
								}, 2000);
							} else {
								this.isWhether = true;
								this.$sun.toast(result.message, 'none');
							}
						}
					}
				})
			},

			// 列表数据加载
			async upCallback(scroll) {
				console.log('upCallback 触发:', {
					tabsId: this.tabsId,
					tabsNextId: this.tabsNextId,
					page: scroll.num,
					userUid: uni.getStorageSync('uid')
				});

				let getUrl = '';
				let getData = {};

				// 根据不同的标签页加载不同的数据

				// 我的形象
				if (this.tabsId == 1) {
					getUrl = this.$api.avatarList;
					getData = {
						uid: uni.getStorageSync('uid'),
						definition: this.tabsNextId == 4 ? 2 : 1,
						page: scroll.num,
						psize: 3
					}
				}

				// 我的作品和成品库
				else if (this.tabsId == 2) {
					let getTypeId = '';
					if (this.tabsNextId == 1) {
						getTypeId = 1;
					}
					if (this.tabsNextId == 2) {
						getTypeId = 3;
					}
					if (this.tabsNextId == 3) {
						getTypeId = 2;
					}
					if (this.tabsNextId == 4) {
						getTypeId = 4;
					}

					getUrl = this.$api.videoList;
					getData = {
						uid: uni.getStorageSync('uid'),
						name: this.name,
						type: getTypeId, // 1 线路一  3 线路二 2 线路三 4 线路四
						page: scroll.num,
						psize: 12
					}
				} else if (this.tabsId == 4) {
					getUrl = this.$api.clipTaskList;
					getData = {
						uid: uni.getStorageSync('uid'),
						page: scroll.num,
						psize: 12
					}
				}

				// 我的音色
				else if (this.tabsId == 3) {
					getUrl = this.$api.voiceTrainList;
					getData = {
						uid: uni.getStorageSync('uid'),
						name: '',
						train_mode: this.tabsNextId, //1-标准模式 2-高保真  3专业版
						current_status: this.tabId,
						page: scroll.num,
						psize: 10
					}
				}

				// 资产市场
				else if (this.tabsId == 5) {
					getUrl = this.$api.goodsList;
					getData = {
						uid: uni.getStorageSync('uid'),
						order_field: '', //sell_money 热门排序 create_time 最新排序
						type: this.tabsNextId == 1 ? 4 : 3, //type 1 高级形象 type ：2 标准形象 type 3声音，4 高级形象和 标准形象
						status: '0', //0全部 1上架 ，2下架
						page: scroll.num,
						psize: 12
					}
				}

				// 检查是否有有效的 URL
				if (!getUrl) {
					console.error('upCallback: 无效的 tabsId 或未配置的 API URL', this.tabsId);
					this.mescroll.endErr();
					return;
				}

				try {
					const result = await this.$http.post({
						url: getUrl,
						data: getData
					});

					if (result.errno == 0) {
						this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
						if (scroll.num == 1) this.list = [];
						this.list = this.list.concat(result.data.list);

						// 为音频列表添加播放状态
						if (this.tabsId == 3) {
							for (let i = 0; i < this.list.length; i++) {
								this.list[i].isPlay = 2;
							}
						}

						// 为资产市场音频列表添加播放状态
						if (this.tabsId == 5 && this.tabsNextId == 2) {
							for (let i = 0; i < this.list.length; i++) {
								this.list[i].isPlay = 2;
							}
						}
					} else {
						// 处理业务错误
						console.error('upCallback: 业务错误', result.message);
						this.mescroll.endErr();
					}
				} catch (error) {
					// 处理网络错误
					console.error('upCallback: 网络错误', error);
					this.mescroll.endErr();
				}
			},

			// 下拉刷新回调
			downCallback() {
				// 默认重置上拉加载列表为第一页
				this.mescroll.resetUpScroll();
			},

			// mescroll初始化完成的回调
			mescrollInit(mescroll) {
				console.log('mescroll 初始化完成:', mescroll);
				this.mescroll = mescroll;

				// 如果用户已登录，立即触发第一次加载
				if (uni.getStorageSync('uid')) {
					console.log('用户已登录，触发首次数据加载');
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}
			},

			// 作品和资产相关操作方法

			// 视频操作相关方法

			// 视频弹窗查看
			getVideoOpen(item) {
				this.obj = item;
				this.$refs.rescuePop.show({
					style: 'width:540rpx; height:960rpx;background-color: #FFFFFF;border-radius: 5px;',
					anim: 'center',
					shadeClose: false, //使用户不能点击其它关闭页面
					bottomClose: true
				});
			},

			// 查看视频详情
			getVideo(obj) {
				if (obj.buy_expire == 1) {
					this.$sun.toast("该形象已过期", 'error');
					return;
				}

				let param = null;

				if ((this.tabsNextId == 1 && obj.current_status == 'completed') || (this.tabsNextId == 2 && obj
						.new_current_status == 'completed') || (this.tabsNextId == 3 && obj.composite_current_status ==
						'completed') || (this.tabsNextId == 4 && obj.four_current_status == 'completed')) {

					//高级 快速版
					param = {
						base_video: obj.video_url,
						decode_img: obj.video_cover,
						id: obj.id,
						isSel: this.tabsNextId,
						current_status: obj.current_status,
						new_current_status: obj.new_current_status,
						composite_current_status: obj.composite_current_status,
						four_current_status: obj.four_current_status
					};

					uni.navigateTo({
						url: '/pages/index/videos?param=' + encodeURIComponent(JSON.stringify(param))
					})
				}
			},

			// 保存视频到相册
			async longpress(local_url, url, status) {
				if (status != 'success') {
					this.$sun.toast("作品保存相册失败!", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				if (url) {
					uni.showLoading({
						mask: true
					})

					uni.downloadFile({
						url: url,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.saveVideoToPhotosAlbum({
									filePath: res.tempFilePath,
									success: (r) => {
										this.isWhether = true;
										uni.hideLoading();
										this.$sun.toast("保存相册成功");
									},
									fail: (e) => {
										if (e.errMsg ==
											'saveVideoToPhotosAlbum:fail auth deny') {
											uni.hideLoading();
											uni.showModal({
												title: '您需要授权相册权限',
												success(res) {
													if (res.confirm) {
														uni.openSetting({
															success(res) {},
															fail(res) {}
														});
													}
												}
											});
										} else if (e.errMsg ==
											'saveVideoToPhotosAlbum:fail file not exists') {
											this.isWhether = true;
											uni.hideLoading();
											this.$sun.toast("保存失败,文件不存在!", 'none');
										} else {
											this.isWhether = true;
											uni.hideLoading();
											console.log("err==>", e);
											this.$sun.toast("保存失败", 'error');
										}
									}
								});
							} else {
								this.isWhether = true;
								uni.hideLoading();
								this.$sun.toast("下载失败", 'error');
							}
						},
						fail: err => {
							uni.hideLoading();
							this.$sun.toast(err, 'error');
						}
					});
				} else {
					this.isWhether = true;
					this.$sun.toast("视频资源异常,下载失败", 'none');
				}
			},

			// 复制链接
			getCopy(url, status) {
				if (status != 'success') {
					this.$sun.toast("链接复制失败!", 'none');
					return;
				}
				uni.setClipboardData({
					data: url,
					success: () => {
						this.$sun.toast('复制成功');
					},
					fail: (err) => {
						console.log("复制失败原因===>", err);
						uni.showToast({
							title: '复制失败：' + err,
							icon: 'none'
						});
					}
				});
			},

			// 剪辑视频列表
			getVideoList(obj) {
				if (obj.status == 4 || obj.status == 6) {
					uni.navigateTo({
						url: '/pages/edit/videoList?id=' + obj.id + '&name=' + obj.title + '&type=1'
					})
				}
			},

			//下载视频
			async getVideoDownloadFile(id) {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				uni.showLoading({
					title: '正在加载视频',
					mask: true
				})

				let getUrl = '';

				// 根据标签页设置不同的API
				if (this.tabsId == 2) {
					getUrl = this.$api.videoToLocal;
				}

				const result = await this.$http.post({
					url: getUrl,
					data: {
						uid: uni.getStorageSync('uid'),
						video_id: id,
					}
				});
				if (result.errno == 0) {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
					uni.hideLoading();
					setTimeout(() => {
						this.isWhether = true;
						this.getVideoOpen(result.data);
					}, 1000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			// 音频操作相关方法

			// 播放音频
			async playAudio(isPlay, index, id, media_url) {
				this.updateKey = false;

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				let getUrl = '';
				let getDate = '';

				getUrl = this.$api.ttsVoice;
				getDate = {
					uid: uni.getStorageSync('uid'),
					voice_id: id,
					text: '这是你的高保真声音克隆效果，你觉得效果怎么样?'
				}

				if (media_url) {
					this.getA(isPlay, index, media_url, 1);
				} else {
					const result = await this.$http.post({
						url: getUrl,
						data: getDate
					});
					if (result.errno == 0) {
						if (result.data) {
							this.getSetDefaultVoice(result.data, id);
							this.getA(isPlay, index, result.data, 2);
						} else {
							this.$sun.toast("音频生成失败,请联系平台处理!", 'none');
							this.isWhether = true;
						}
					} else {
						if (result.errno == -1) {
							this.$sun.toast("音频播放失败,请重新点击播放", 'none');
						} else {
							this.$sun.toast(result.message, 'none');
						}
						this.isWhether = true;
					}
				}
			},

			// 播放音频处理
			getA(isPlay, index, media_url, type) {
				console.log("isPlay---->", isPlay, media_url);

				uni.showLoading({
					title: '正在试听...',
					mask: true
				})

				this.voiceAudioContext = null;
				this.voiceAudioContext = uni.createInnerAudioContext();
				this.voiceAudioContext.src = media_url;

				setTimeout(() => {
					if (isPlay == 2) {
						this.list[index].isPlay = 1;
						console.log("----->", this.list[index].isPlay);
						this.updateKey = true;
						this.voiceAudioContext.play();
						this.voiceAudioContext.onPlay(() => {});
						this.voiceAudioContext.onEnded(() => {
							this.list[index].isPlay = 2;
							uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.$sun.toast("试听完成");
							this.updateKey = true;
							this.isWhether = true;
							if (type == 2) {
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 1000);
							}
						});
						this.voiceAudioContext.onError((err) => {
							this.$sun.toast("音频播放出错:" + err, 'none');
							uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.isWhether = true;
							this.updateKey = true;
						});
					} else {
						this.list[index].isPlay = 2;
						this.isWhether = true;
						this.updateKey = true;
						this.voiceAudioContext.pause();
						this.voiceAudioContext.onPause(() => {});
					}
				}, 500);
			},

			// 设置试听音频
			async getSetDefaultVoice(aUrl, id) {
				const result = await this.$http.post({
					url: this.$api.setDefaultVoice,
					data: {
						uid: uni.getStorageSync('uid'),
						voice_id: id,
						media_url: aUrl, // 音频链接 
					}
				});
				if (result.errno == 0) {
					this.isWhether = true;
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			// 删除操作相关方法

			// 删除视频/形象/资产
			delVideo(obj) {
				let getUrl = '';
				let data = '';
				let objName = '';

				// 根据不同标签页处理不同的删除逻辑
				if (this.tabsId == 2) {
					getUrl = this.$api.videoDel;
					data = {
						uid: uni.getStorageSync('uid'),
						video_id: obj.id,
						task_id: obj.id,
					};
					objName = obj.name;
				} else if (this.tabsId == 4) {
					getUrl = this.$api.deleteMission;
					data = {
						uid: uni.getStorageSync('uid'),
						id: obj.id
					};
					objName = obj.title;
				} else if (this.tabsId == 1) {
					getUrl = this.$api.avatarDel;
					data = {
						uid: uni.getStorageSync('uid'),
						avatar_id: obj.id
					};
					objName = obj.name;
				} else if (this.tabsId == 5) {
					getUrl = this.$api.goodsDelete;
					data = {
						uid: uni.getStorageSync('uid'),
						goods_id: obj.id
					};
					objName = obj.title;
				}

				// 显示确认对话框
				let confirmMessage = this.tabsId == 1 ?
					'将删除1.2.3条线路形象,确认删除 ' + objName + ' 吗?' :
					'确认删除 ' + objName + ' 吗?';

				uni.showModal({
					title: '提示',
					content: confirmMessage,
					success: async (res) => {
						if (res.confirm) {
							// 如果是从弹窗打开的，关闭弹窗
							this.closeBatch();

							const result = await this.$http.post({
								url: getUrl,
								data: data
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						}
					}
				});
			},

			// 删除音频
			delAudio(id, name) {
				uni.showModal({
					title: '提示',
					content: '确认删除 ' + name + ' 吗?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.voiceTrainDel,
								data: {
									uid: uni.getStorageSync('uid'),
									voice_id: id
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						}
					}
				});
			},

			// 批量操作相关

			// 更多操作弹窗
			getBatch(obj) {
				this.obj = obj;
				this.$refs.pop2.show({
					style: 'background-color:#fff;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},

			// 提交克隆
			async getStartTraining(id) {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.avatarClone,
					data: {
						uid: uni.getStorageSync('uid'),
						way: Number(this.tabsNextId) + 1, //2 线路一 3线路二 4线路三 5线路四
						id: id
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			// 共享资产相关方法

			// 打开协议
			openProtocol() {
				this.closeShare();
				this.isProtocol = true;

				this.$refs.pop.show({
					title: this.tabsId == 3 ? '共享声音资产协议' : '共享形象资产协议',
					style: 'background-color:#fff;width:700rpx;border-radius:10rpx;',
					shadeClose: false,
				});
			},

			// 关闭协议弹窗
			close() {
				this.$refs.pop.close();
				this.getShare(3, '');
			},

			// 协议勾选状态切换
			getIsProtocol() {
				this.isProtocol = !this.isProtocol;
			},

			// 打开共享资产弹窗
			getShare(type, obj) {
				if (type == 1) {
					this.closeBatch();
				}
				if (type == 2) {
					this.obj = obj;
				}
				if (type == 4) {
					this.title = obj.title;
					this.price = obj.price;
					this.assetsId = obj.id;
				}
				this.$refs.pop3.show({
					style: 'background-color:#000;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},

			// 选择时间段价格
			getTabs(obj, index) {
				this.priceIndex = index;
			},

			// 确认共享资产
			async getReleaseGood() {
				if (!this.title) {
					this.$sun.toast("请输入共享标题", 'none');
					return;
				}

				for (let i = 0; i < this.price.length; i++) {
					if (!this.price[i].price) {
						this.$sun.toast("请设置" + this.price[i].name + "的共享时长价格", 'none');
						return;
					}
				}

				if (!this.assetsId) {
					if (!this.isProtocol) {
						this.$sun.toast("请阅读并勾选协议", 'none');
						return;
					}
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				let getUrl = '';
				let getData = '';

				if (this.assetsId) {
					getUrl = this.$api.editGoods;
					getData = {
						uid: uni.getStorageSync('uid'),
						title: this.title,
						price: this.price,
						id: this.assetsId,
					};
				} else {
					getUrl = this.$api.releaseGood;
					getData = {
						uid: uni.getStorageSync('uid'),
						title: this.title,
						price: this.price,
						relation_id: this.obj.id, //type 1 传高级形象id type ：2 传标准形象id type 3 传 声音id
						type: this.tabsId, //type=1形像;type=3 声音
					};
				}

				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.title = '';
						this.price = '';
						this.closeShare();
						if (this.assetsId) {
							this.assetsId = '';
							this.$nextTick(() => {
								this.mescroll.resetUpScroll();
							});
						} else {
							if (this.tabsId == 1) {
								this.tabsNextId = 2;
							}
							this.getId(this.tabs[this.tabs.length - 1]);
						}
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			// 音频重新提交
			getAgain(obj) {
				uni.navigateTo({
					url: '/pages/index/voice/senior?name=' + obj.name + '&sex=' + obj.sex + '&vid=' + obj.id
				})
			},
		}
	}
</script>

<style lang="scss">
	.list2 {
		display: grid;
		grid-template-columns: 342rpx auto;
		grid-template-rows: 144rpx 144rpx;
		grid-template-areas:
			"item1 item2"
			"item1 item3";
		gap: 16rpx;
		padding: 0 20rpx;
		margin-bottom: 20rpx;


		&-item {
			position: relative;
			border: 4rpx rgba(100, 82, 58, .4) solid;
			background: linear-gradient(134deg, #493721 0%, #2d241f 100%);
			color: #fbe598;
			border-radius: 20rpx;
			padding: 20rpx 32rpx;

			.img {
				width: 80rpx;
				height: 80rpx;
			}

			.title {
				font-size: 32rpx;
				margin-bottom: 10rpx;
			}


			.desc {
				color: #8b7b6b;
				font-size: 22rpx;
			}

			&:nth-child(1) {
				.img {
					width: 140rpx;
					height: 140rpx;
					position: absolute;
					bottom: 20rpx;
					right: 20rpx;
				}
			}

			&:nth-child(2),
			&:nth-child(3) {
				.img {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					left: 20rpx;
				}

				.title,
				.desc {
					margin-left: 80rpx;
				}
			}
		}
	}

	// 页面基础样式
	page {
		border-top: none;
		background-color: #111317;
		overflow-x: hidden;
	}

	// 子标签栏样式
	.sub-tabbar-container {
		margin-bottom: 20rpx;
		margin-top: 20rpx;
		padding: 0 10rpx;
		overflow: hidden;
	}

	.sub-tabbar-scroll {
		width: 100%;
		white-space: nowrap;
		overflow-x: auto;
		scrollbar-width: none;
		-ms-overflow-style: none;

		&::-webkit-scrollbar {
			display: none;
		}
	}

	.sub-tabbar-wrapper {
		display: inline-flex;
		padding: 0 20rpx;
	}

	.sub-tabbar-item {
		padding: 12rpx 30rpx;
		margin-right: 20rpx;
		border-radius: 30rpx;
		background-color: #232323;
		color: #B2B1B1;
		font-size: 28rpx;
		transition: all 0.2s ease;
		text-align: center;
		min-width: 100rpx;
	}

	.sub-tabbar-item-active {
		background: linear-gradient(134deg, #fedd96 0%, #face6b 100%);
		color: #1f0c00;
		// box-shadow: 0 2rpx 8rpx rgba(30, 108, 235, 0.3);
	}

	// 通用样式和布局
	.display-a {
		display: flex;
		align-items: center;
	}

	.display-a-jc {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.display-a-js {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.display-ac-jc {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.display-fw-a {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	// 无数据提示样式
	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	// 客服按钮样式
	.kefu-2 {
		right: -50rpx;
		opacity: 0.5;
		transition: all 1s linear;
	}

	.kefu-1 {
		right: 10rpx;
		transition: all 1s linear;
	}

	.kefu {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		z-index: 99;
		bottom: 240rpx;
	}

	// 客服二维码样式
	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.h_30rpx {
		height: 30rpx;
	}

	// 形象和视频相关样式
	.frame,
	.r-frame-w {
		position: relative;
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx 10rpx 0 0;
		// margin-right: 10rpx;
		overflow: hidden;
		display: block;
	}

	.r-frame-w {
		margin-bottom: 0;
	}

	.frame-title {
		background-color: #414141;
		padding: 10rpx;
		width: 344rpx;
		border-radius: 0 0 10rpx 10rpx;
		margin-bottom: 20rpx;
	}

	.r-video,
	.r-video-w {
		width: 344rpx;
		border-radius: 10rpx 10rpx 0 0;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.r-video-w {
		position: absolute;
		z-index: 1;
	}

	.img-129,
	.img-158 {
		width: 344rpx;
		height: 612rpx;
		position: relative;
		z-index: 1;
		border-radius: 10rpx 10rpx 0 0;
	}

	.img-158 {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		z-index: 2;
		top: 49%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	// 状态标签样式
	.r-status {
		width: 90rpx;
		padding: 6rpx 0;
		text-align: center;
		border-radius: 10rpx 0 10rpx 0;
		position: absolute;
		top: 0;
		left: 0;
		font-size: 24rpx;
		color: #FFF;
		z-index: 4;
	}

	.r-status-1 {
		background-color: #1377FF;
	}

	.r-status-2 {
		background-color: #07BD00;
	}

	.r-status-3 {
		background-color: #FF0000;
	}

	.r-status-4 {
		background-color: #565756;
	}

	// 我的音色相关样式
	.list-public {
		background-color: #333333;
		padding: 50rpx 30rpx 20rpx;
		position: relative;
	}

	.r-fast-1 {
		background: linear-gradient(104.04deg, rgb(228, 113, 247) 1.66%, rgb(100, 27, 227) 96.68%);
	}

	.r-fast-2 {
		background: linear-gradient(90.00deg, rgb(0, 177, 255), rgb(0, 82, 255) 100%);
	}

	.r-fast {
		width: 84rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		text-align: center;
		color: #FFF;
		margin-right: 10rpx;
		padding: 2rpx 0;
	}

	.r-name {
		font-size: 30rpx;
		font-weight: 600;
		color: #FFF;
		width: 320rpx;
	}

	.r-date {
		color: #AAA9A9;
		font-size: 26rpx;
	}

	.ra-status {
		position: absolute;
		top: 0;
		right: 0;
		width: 86rpx;
		text-align: center;
		border-radius: 0 10rpx 0 10rpx;
		background-color: #6B6B6B;
		padding: 6rpx 0;
		font-size: 24rpx;
	}

	.ra-status-1 {
		color: #B4B4B4;
	}

	.color_FF0000 {
		color: #FF0000;
	}

	.color_FFFFFF {
		color: #FFFFFF;
	}

	.reject {
		padding-top: 20rpx;
		font-size: 26rpx;
		color: #FF6767;
		border-top: 1px solid rgb(65, 65, 65);
	}

	.again {
		width: 140rpx;
		text-align: center;
		color: #4183FF;
		border: 1px solid #4183FF;
		border-radius: 10rpx;
		padding: 10rpx 0;
		font-size: 26rpx;
		margin-left: auto;
	}

	.service-life,
	.service-life-2 {
		position: absolute;
		top: 374rpx;
		left: 4rpx;
		z-index: 4;
		color: #ff0000;
	}

	.service-life-2 {
		position: absolute;
		top: 2rpx;
		left: 4rpx;
	}

	// 图片和图标样式
	.img-347 {
		width: 46rpx;
		height: 46rpx;
		margin-bottom: 4rpx;
	}

	.img-96 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 6rpx;
		margin-left: 30rpx;
	}

	.img-246 {
		width: 70rpx;
		height: 70rpx;
		margin-right: 20rpx;
	}

	.img-251 {
		width: 40rpx;
		height: 40rpx;
		margin-left: auto;
		margin-right: 10rpx;
	}

	.img-59 {
		width: 32rpx;
		height: 32rpx;
		margin-left: auto;
	}

	.img-362 {
		width: 40rpx;
		height: 40rpx;
	}

	// 失败提示样式
	.failed-tips {
		position: absolute;
		z-index: 30;
		width: 344rpx;
		height: 612rpx;
		background-color: rgba(50, 50, 50, .6);
		color: #FF0000;
		font-size: 26rpx;
		padding: 0 20rpx;
		word-break: break-word;
	}

	.r-img-bg {
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx 10rpx 0 0;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.r-text {
		text-align: center;
		font-size: 26rpx;
		padding-top: 440rpx;
	}

	// 资产市场相关样式
	.shelves-1 {
		width: 160rpx;
		background: linear-gradient(270.00deg, rgb(0, 114, 255), rgb(0, 158, 255) 100%);
	}

	.shelves-2 {
		width: 184rpx;
		background: linear-gradient(270.00deg, rgb(255, 0, 0), rgb(255, 97, 97) 100%);
	}

	.shelves {
		text-align: center;
		color: #FFF;
		border-radius: 100rpx;
		padding: 14rpx 0;
	}

	.img-252,
	.img-253 {
		width: 60rpx;
		height: 60rpx;
		border-radius: 100rpx;
		margin-right: 20rpx;
	}

	.img-253 {
		margin-left: 30rpx;
	}

	.img-266 {
		width: 140rpx;
		height: 140rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
	}

	.img-254 {
		position: absolute;
		z-index: 9;
		width: 140rpx;
		height: 140rpx;
		border-radius: 10rpx;
		left: 20rpx;
		top: 20rpx;
	}

	.padding_20rpx {
		padding: 20rpx;
	}

	// 成品库相关样式
	.task-list {
		margin: 0 20rpx 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1px solid rgb(41, 41, 41);
	}

	.img-306 {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
	}

	.img-21 {
		width: 24rpx;
		height: 24rpx;
		margin-left: 6rpx;
	}

	.w-status {
		width: 110rpx;
		text-align: center;
		padding: 2rpx 0;
		font-size: 24rpx;
		color: #fff;
		border-radius: 10rpx;
		margin-left: 10rpx;
	}

	.w-status-1 {
		background-color: #1377FF;
	}

	.w-status-2 {
		background-color: #07BD00;
	}

	.w-status-3 {
		background-color: #FF0000;
	}

	// 共享资产弹窗相关样式
	.pop-bg {
		padding: 34rpx 24rpx;
		color: #FFF;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.p-title {
		color: #FFF;
		font-weight: 600;
		font-size: 40rpx;
		text-align: center;
		margin-bottom: 40rpx;
	}

	.p-input {
		width: 660rpx;
		padding: 20rpx;
		border: 1px solid rgb(35, 148, 251);
		border-radius: 10rpx;
		background: rgb(24, 24, 24);
	}

	.c-agree {
		width: 690rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
	}

	.tabs-a-1 {
		color: #FFF;
	}

	.tabs-a-2 {
		color: #437EFF;
		border: 1px solid #437EFF;
	}

	.tabs-a {
		width: 190rpx;
		text-align: center;
		border-radius: 10rpx;
		background-color: #181818;
		padding: 20rpx 0;
		margin-right: 20rpx;
	}

	.color_4BA2FF {
		color: #4BA2FF;
		font-size: 26rpx;
	}

	.color_CBCACA {
		color: #cbcaca;
		font-size: 26rpx;
	}

	.img-219 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 12rpx;
	}

	.p-data {
		padding: 40rpx 0;
		text-align: center;
		width: 750rpx;
	}

	.p-bo {
		border-bottom: 1px solid #f0f0f0;
	}

	.p-bo2 {
		border-bottom: 1px solid #232323;
	}

	// 通用布局样式
	.display-a {
		display: flex;
		align-items: center;
	}

	.display-a-jc {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.display-a-js {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.display-ac-jc {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.display-fw-a {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.margin-left-auto {
		margin-left: auto;
	}

	.margin-bottom_10rpx {
		margin-bottom: 10rpx;
	}

	.margin-bottom_20rpx {
		margin-bottom: 20rpx;
	}

	.margin-bottom_30rpx {
		margin-bottom: 30rpx;
	}

	.margin-bottom_40rpx {
		margin-bottom: 40rpx;
	}

	.margin_0_4rpx {
		margin: 0 4rpx;
	}

	.margin-top_20rpx {
		margin-top: 20rpx;
	}

	.margin-left_10rpx {
		margin-left: 10rpx;
	}

	.text-align_center {
		text-align: center;
	}

	.font-overflow {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.font-size_24rpx {
		font-size: 24rpx;
	}

	.font-size_26rpx {
		font-size: 26rpx;
	}

	.font-size_30rpx {
		font-size: 30rpx;
	}

	.font-size_32rpx {
		font-size: 32rpx;
	}

	.font-weight_bold {
		font-weight: bold;
	}

	/* 分类标签栏样式 */
	.tabbar-container {
		background-color: #1B1B1B;
		padding: 20rpx 0;
		border-bottom: 1px solid #333;
	}

	.tabbar-scroll {
		width: 100%;
		white-space: nowrap;
	}

	.tabbar-wrapper {
		display: flex;
		padding: 0 20rpx;
	}

	.tabbar-item {
		position: relative;
		margin-right: 40rpx;
		padding: 10rpx 20rpx;
		flex-shrink: 0;
	}

	.tabbar-item-active {
		/* background-color: rgba(228, 150, 253, 0.1); */
		border-radius: 20rpx;
	}

	.tabbar-text {
		font-size: 28rpx;
		color: #999;
		transition: color 0.3s;
	}

	.tabbar-item-active .tabbar-text {
		color: #FFF;
		/* font-weight: bold; */
	}

	.tabbar-line {
		position: absolute;
		bottom: -2rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 6rpx;
		background: linear-gradient(90.00deg, #fedd96, #face6b 100%);
		border-radius: 3rpx;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 2rpx 8rpx rgba(0, 177, 255, 0.3);
	}

	.tabbar-line-active {
		width: calc(100% - 20rpx);
		box-shadow: 0 2rpx 12rpx rgba(0, 177, 255, 0.5);
	}
</style>